"""
Client code demonstrating the Composite Design Pattern implementation.

This module shows how to use the File and Folder classes to create
a hierarchical file system structure and display it.
"""

from .file import File
from .folder import Folder


def main() -> None:
    """
    Demonstrate the Composite Design Pattern with a file system example.
    
    Creates a hierarchical structure of folders and files, then displays
    the entire structure to show how the pattern works.
    """
    # Root folder
    root = Folder('root')
    
    # Files directly in root
    root.add(File('README.md'))
    root.add(File('LICENSE'))
    
    # Subfolders with their own contents
    src = Folder('src')
    src.add(File('main.py'))
    src.add(File('utils.py'))
    
    # Nested folder
    lib = Folder('lib')
    lib.add(File('math.py'))
    src.add(lib)
    
    root.add(src)
    
    # Additional folder structure
    tests = Folder('tests')
    tests.add(File('test_main.py'))
    tests.add(File('test_utils.py'))
    
    # Nested test folder
    integration = Folder('integration')
    integration.add(File('test_integration.py'))
    tests.add(integration)
    
    root.add(tests)
    
    # Display the entire structure
    print("File System Structure:")
    print("=" * 30)
    root.display()


if __name__ == '__main__':
    main()
