"""
FileSystem Composite Pattern Implementation

This package demonstrates the Composite Design Pattern using a file system metaphor.
The pattern allows you to compose objects into tree structures to represent part-whole hierarchies.

Classes:
    Component: Abstract base class defining the common interface
    File: Leaf component representing individual files
    Folder: Composite component that can contain other components
"""

from .component import Component
from .file import File
from .folder import Folder

__all__ = ['Component', 'File', 'Folder']