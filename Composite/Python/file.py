"""
File class representing a leaf component in the Composite Design Pattern.

This module implements the File class which represents individual files
in the file system hierarchy.
"""

from .component import Component


class File(Component):
    """
    Represents a file in the file system.
    
    This is a leaf component that implements the Component interface.
    Files cannot contain other components.
    """
    
    def __init__(self, name: str) -> None:
        """
        Initialize a new File instance.
        
        Args:
            name: The name of the file
        """
        self._name = name
    
    @property
    def name(self) -> str:
        """Get the name of the file."""
        return self._name
    
    def display(self, indent: str = '') -> None:
        """
        Display the file name with the given indentation.
        
        Args:
            indent: String used for indentation to show hierarchy
        """
        print(f"{indent}{self._name}")
