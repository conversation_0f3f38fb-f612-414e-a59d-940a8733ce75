"""
Component interface for the Composite Design Pattern.

This module defines the abstract base class that both File and Folder classes implement.
"""

from abc import ABC, abstractmethod


class Component(ABC):
    """
    Abstract base class defining the interface for all components in the file system.
    
    This class declares the interface for objects in the composition and implements
    default behavior for the interface common to all classes as appropriate.
    """
    
    @abstractmethod
    def display(self, indent: str = '') -> None:
        """
        Display the component with the given indentation.
        
        Args:
            indent: String used for indentation to show hierarchy
        """
        pass
