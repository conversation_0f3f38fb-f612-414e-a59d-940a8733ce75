"""
Unit tests for the FileSystem Composite Pattern implementation.

This module contains tests to verify that the Composite pattern implementation
works correctly and matches the expected behavior.
"""

import unittest
from io import StringIO
import sys
from .file import File
from .folder import Folder


class TestFileSystemComposite(unittest.TestCase):
    """Test cases for the FileSystem Composite Pattern implementation."""
    
    def setUp(self):
        """Set up test fixtures before each test method."""
        self.root = Folder('root')
        self.file1 = File('README.md')
        self.file2 = File('LICENSE')
        self.src_folder = Folder('src')
        self.lib_folder = Folder('lib')
    
    def test_file_creation(self):
        """Test that files can be created with names."""
        file = File('test.txt')
        self.assertEqual(file.name, 'test.txt')
    
    def test_folder_creation(self):
        """Test that folders can be created with names."""
        folder = Folder('test_folder')
        self.assertEqual(folder.name, 'test_folder')
        self.assertEqual(len(folder.children), 0)
    
    def test_folder_add_file(self):
        """Test that files can be added to folders."""
        folder = Folder('test')
        file = File('test.txt')
        folder.add(file)
        self.assertEqual(len(folder.children), 1)
        self.assertIn(file, folder.children)
    
    def test_folder_add_folder(self):
        """Test that folders can be added to other folders."""
        parent = Folder('parent')
        child = Folder('child')
        parent.add(child)
        self.assertEqual(len(parent.children), 1)
        self.assertIn(child, parent.children)
    
    def test_folder_remove_component(self):
        """Test that components can be removed from folders."""
        folder = Folder('test')
        file = File('test.txt')
        folder.add(file)
        folder.remove(file)
        self.assertEqual(len(folder.children), 0)
        self.assertNotIn(file, folder.children)
    
    def test_folder_remove_nonexistent_component(self):
        """Test that removing a non-existent component raises ValueError."""
        folder = Folder('test')
        file = File('test.txt')
        with self.assertRaises(ValueError):
            folder.remove(file)
    
    def test_file_display(self):
        """Test that file display works correctly."""
        file = File('test.txt')
        captured_output = StringIO()
        sys.stdout = captured_output
        file.display('  ')
        sys.stdout = sys.__stdout__
        self.assertEqual(captured_output.getvalue(), '  test.txt\n')
    
    def test_folder_display(self):
        """Test that folder display works correctly with nested structure."""
        # Create structure similar to PHP example
        root = Folder('root')
        root.add(File('README.md'))
        root.add(File('LICENSE'))
        
        src = Folder('src')
        src.add(File('main.py'))
        src.add(File('utils.py'))
        
        lib = Folder('lib')
        lib.add(File('math.py'))
        src.add(lib)
        
        root.add(src)
        
        # Capture output
        captured_output = StringIO()
        sys.stdout = captured_output
        root.display()
        sys.stdout = sys.__stdout__
        
        output_lines = captured_output.getvalue().strip().split('\n')
        expected_lines = [
            'root/',
            '  README.md',
            '  LICENSE',
            '  src/',
            '    main.py',
            '    utils.py',
            '    lib/',
            '      math.py'
        ]
        
        self.assertEqual(output_lines, expected_lines)
    
    def test_composite_pattern_uniformity(self):
        """Test that files and folders can be treated uniformly."""
        components = [File('file.txt'), Folder('folder')]
        
        # Both should have display method
        for component in components:
            self.assertTrue(hasattr(component, 'display'))
            self.assertTrue(callable(getattr(component, 'display')))


if __name__ == '__main__':
    unittest.main()
