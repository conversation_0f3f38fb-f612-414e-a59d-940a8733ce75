# Composite Design Pattern - FileSystem Implementation (Python)

This directory contains a Python implementation of the Composite Design Pattern using a file system metaphor, equivalent to the PHP implementation in the parent directory.

## Pattern Overview

The Composite pattern allows you to compose objects into tree structures to represent part-whole hierarchies. It lets clients treat individual objects and compositions of objects uniformly.

## Structure

### Components

1. **`Component`** (`component.py`) - Abstract base class defining the interface
   - Declares the interface for objects in the composition
   - Uses Python's ABC (Abstract Base Classes) for proper abstraction

2. **`File`** (`file.py`) - Leaf component
   - Represents individual files in the file system
   - Implements the Component interface
   - Cannot contain other components

3. **`Folder`** (`folder.py`) - Composite component
   - Represents folders that can contain other components
   - Can contain both files and other folders
   - Implements child management operations (add, remove)

## Key Features

- **Type Hints**: Full type annotation support for better IDE integration
- **Properties**: Pythonic property access for names and children
- **Error Handling**: Proper exception handling for invalid operations
- **Documentation**: Comprehensive docstrings following Python conventions
- **Testing**: Complete unit test suite

## Usage

### Basic Usage

```python
from Composite.FileSystem.Python import File, Folder

# Create a folder structure
root = Folder('root')
root.add(File('README.md'))
root.add(File('LICENSE'))

# Create nested structure
src = Folder('src')
src.add(File('main.py'))
root.add(src)

# Display the structure
root.display()
```

### Running the Demo

```bash
# Run the client demo
python3 -m Composite.FileSystem.Python.client

# Run the tests
python3 -m unittest Composite.FileSystem.Python.test_filesystem -v
```

## Files

- `__init__.py` - Package initialization and exports
- `component.py` - Abstract Component interface
- `file.py` - File (leaf) implementation
- `folder.py` - Folder (composite) implementation
- `client.py` - Demonstration client code
- `test_filesystem.py` - Unit tests
- `README.md` - This documentation

## Differences from PHP Implementation

1. **Abstract Base Classes**: Uses Python's `abc` module instead of interfaces
2. **Type Hints**: Includes comprehensive type annotations
3. **Properties**: Uses Python properties for attribute access
4. **Error Handling**: More explicit exception handling
5. **Testing**: Includes comprehensive unit tests
6. **Documentation**: Extensive docstrings following Python conventions

## Design Benefits

1. **Uniform Treatment**: Files and folders can be treated uniformly through the Component interface
2. **Recursive Composition**: Folders can contain other folders, creating tree structures
3. **Easy Extension**: New component types can be added easily
4. **Client Simplicity**: Clients don't need to distinguish between leaf and composite objects
