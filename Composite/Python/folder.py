"""
Folder class representing a composite component in the Composite Design Pattern.

This module implements the Folder class which can contain other components
(both files and other folders) in the file system hierarchy.
"""

from typing import List
from .component import Component


class Folder(Component):
    """
    Represents a folder in the file system.
    
    This is a composite component that can contain other components.
    Folders can contain both files and other folders, creating a tree structure.
    """
    
    def __init__(self, name: str) -> None:
        """
        Initialize a new Folder instance.
        
        Args:
            name: The name of the folder
        """
        self._name = name
        self._children: List[Component] = []
    
    @property
    def name(self) -> str:
        """Get the name of the folder."""
        return self._name
    
    @property
    def children(self) -> List[Component]:
        """Get a copy of the children list."""
        return self._children.copy()
    
    def add(self, component: Component) -> None:
        """
        Add a component (file or folder) to this folder.
        
        Args:
            component: The component to add to this folder
        """
        self._children.append(component)
    
    def remove(self, component: Component) -> None:
        """
        Remove a component from this folder.
        
        Args:
            component: The component to remove from this folder
            
        Raises:
            ValueError: If the component is not found in this folder
        """
        try:
            self._children.remove(component)
        except ValueError:
            raise ValueError(f"Component not found in folder '{self._name}'")
    
    def display(self, indent: str = '') -> None:
        """
        Display the folder and all its contents with proper indentation.
        
        Args:
            indent: String used for indentation to show hierarchy
        """
        print(f"{indent}{self._name}/")
        for child in self._children:
            child.display(indent + "  ")
