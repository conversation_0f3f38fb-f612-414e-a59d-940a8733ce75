<?php

namespace FileSystem;

require_once 'File.php';
require_once 'Folder.php';

// Root folder
$root = new Folder('root');

// Files directly in root
$root->add(new File('README.md'));
$root->add(new File('LICENSE'));

// Subfolders with its own contents
$src = new Folder('src');
$src->add(new File('main.php'));
$src->add(new File('utils.php'));

// Nested folder
$lib = new Folder('lib');
$lib->add(new File('math.php'));
$src->add($lib);

$root->add($src);

// Display structure
$root->display();
