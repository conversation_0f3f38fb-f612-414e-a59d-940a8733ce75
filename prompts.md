Below are **5 interview-friendly prompts**, each tailored to one of the foundational patterns (Factory Method, Strategy, Decorator, Singleton, and Command). Each includes:

* A **realistic interview prompt** (short, testable, \~20-min scope)
* An explanation of **why** that pattern fits
* A cue on **what to listen for** during the interview to recognize the pattern

---

### 🏭 1. **Factory Method**

#### 🔹 Prompt:

> "You're building a notification system. Start by supporting email and SMS, but we may want to add more later. Write code that can create the correct type of notification sender given a type string, like `'email'` or `'sms'`."

#### 🧠 Why Factory Method?

* The prompt hints at **polymorphic object creation** based on a type.
* The phrase “we may want to add more later” implies the need for **extensibility without modifying existing code**—a key signpost for Factory Method.

#### 🎯 Pattern Cue:

> If you're being asked to instantiate one of several related classes based on a parameter—**and those classes share an interface**—Factory Method is your tool.

---

### 🧠 2. **Strategy**

#### 🔹 Prompt:

> "We're building a password strength checker. Write a function that can accept different strategies for how to validate a password—length-based, entropy-based, or pattern-based."

#### 🧠 Why Strategy?

* The prompt clearly describes **multiple interchangeable algorithms** for a single task.
* You want to define a **common interface**, then **swap in different behaviors** without changing client code.

#### 🎯 Pattern Cue:

> If you're solving a problem where **you'll need to swap algorithms at runtime**, or test different behaviors behind a common interface—reach for Strategy.

---

### 🎁 3. **Decorator**

#### 🔹 Prompt:

> "We have a basic request handler class. Now we want to add logging, caching, and authentication features to it—but we should be able to combine these in any order without changing the handler code."

#### 🧠 Why Decorator?

* They want to **add functionality dynamically**, without modifying the original class.
* They want to **combine features in arbitrary ways**, which screams **wrapping and composition**—i.e., Decorators.

#### 🎯 Pattern Cue:

> If the interview includes phrases like *“add features without changing code,”* *“optional functionality,”* or *“combine behaviors at runtime,”*—think Decorator.

---

### 👑 4. **Singleton**

#### 🔹 Prompt:

> "We need a class that manages our application-wide configuration or database connection. There should only ever be one instance, and all parts of the app should use the same one."

#### 🧠 Why Singleton?

* A class with a **global point of access**, used throughout the app.
* The requirement of **only one instance ever** is the dead giveaway.

#### 🎯 Pattern Cue:

> If the problem involves **shared state**, **a single coordinated instance**, or **global access**—you're probably being invited to use a Singleton.

---

### 🕹 5. **Command**

#### 🔹 Prompt:

> "We're implementing an undo/redo system for a simple drawing app. Each user action (like drawing a line or erasing) should be reversible."

#### 🧠 Why Command?

* Each action (draw, erase, move) can be encapsulated as an **object**, stored, and replayed later.
* The requirement to **support undo/redo** is a classic hallmark of the Command pattern.

#### 🎯 Pattern Cue:

> Anytime you’re dealing with **actions as first-class objects**, especially for queuing, scheduling, logging, or undo—Command is your best friend.

---
