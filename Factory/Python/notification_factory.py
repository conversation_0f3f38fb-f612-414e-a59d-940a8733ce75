from .email_sender import <PERSON>ail<PERSON>ender
from .sms_sender import SmsSender
from .radio_sender import RadioSender
from .notification_sender import NotificationSender


class NotificationFactory:
    @staticmethod
    def create_sender(type_: str) -> NotificationSender:
        """Factory method to create a notification sender based on the type."""
        if type_ == "email":
            return EmailSender()
        elif type_ == "sms":
            return SmsSender()
        elif type_ == "radio":
            return RadioSender()
        else:
            raise ValueError(f"Unknown notification type: {type_}")
