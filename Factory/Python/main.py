from notification_factory import NotificationFactory


def main():
    # Create an email sender
    type_ = "radio"  # radio, sms, email
    email_sender = NotificationFactory.create_sender(type_)
    email_sender.send(
        "<EMAIL>",
        "This is Major Tom to Ground Control: I’m stepping through the door, and I’m floating in a most peculiar way. And the stars look very different today.",
    )
