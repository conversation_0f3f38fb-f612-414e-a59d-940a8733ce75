<?php

namespace NotificationSystem;

require_once 'EmailSender.php';
require_once 'SmsSender.php';
require_once 'RadioSender.php';

class NotificationFactory {
    public static function createSender(string $type): NotificationSender {
        return match (strtolower($type)) {
            'email' => new EmailSender(),
            'sms' => new SmsSender(),
            'radio' => new RadioSender(),
            default => throw new \InvalidArgumentException("Unknown notification type: {$type}"),
        };
    }
}
