from payment_processor import PaymentProcessor


class Checkout:
    """Client class that processes payments using the PaymentProcessor interface."""
    
    def process_payment(self, payment_processor: PaymentProcessor, amount: float) -> None:
        """Process a payment through the given payment processor."""
        print(f"Charging customer: ${amount}")
        payment_processor.process_payment(amount)
