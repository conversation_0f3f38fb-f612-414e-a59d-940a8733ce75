from payment_processor import PaymentProcessor
from legacy_payment import LegacyPayment


class LegacyPaymentAdapter(PaymentProcessor):
    """Adapter that makes LegacyPayment compatible with PaymentProcessor interface."""
    
    def __init__(self, legacy_payment: LegacyPayment) -> None:
        """Initialize the adapter with a legacy payment instance."""
        self._legacy_payment = legacy_payment
    
    def process_payment(self, amount: float) -> None:
        """Convert dollar amount to cents and use legacy payment system."""
        money_in_cents = int(round(amount * 100))
        self._legacy_payment.make_payment(money_in_cents)
