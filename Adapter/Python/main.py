#!/usr/bin/env python3
"""
Demonstration of the Adapter Design Pattern in Python.

This example shows how to use an adapter to make a legacy payment system
compatible with a modern payment processor interface.
"""

from legacy_payment import LegacyPayment
from legacy_payment_adapter import LegacyPaymentAdapter
from checkout import Checkout


def main():
    """Main function demonstrating the Adapter pattern."""
    # Create instances
    legacy = LegacyPayment()
    adapter = LegacyPaymentAdapter(legacy)
    
    checkout = Checkout()
    checkout.process_payment(adapter, 29.99)


if __name__ == "__main__":
    main()
