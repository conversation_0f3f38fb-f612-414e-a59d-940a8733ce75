<?php

namespace Decorator;

require_once 'Service.php';

class CacheDecorator implements Service {

    private ?string $cachedResult = null;

    public function __construct(private Service $wrapped) {}

    public function execute(): string {
        if($this->cachedResult !== null) {
            return "[Cache] Returning from cache: " . $this->cachedResult;
        }

        $this->cachedResult = $this->wrapped->execute();
        return "[Cache] Caching result: " . $this->cachedResult;
    }
}