<?php

namespace Decorator;

require_once 'BaseService.php';
require_once 'LoggerDecorator.php';
require_once 'CacheDecorator.php';
require_once 'AuthDecorator.php';

// Compose decorators
$service = new BaseService();
$withLogging = new LoggerDecorator($service);
$withAuth = new AuthDecorator($withLogging, true);
$withCache = new CacheDecorator($withAuth);

// Execute the service
echo $withCache->execute() . "\n";

// Second call should hit cache
echo $withCache->execute() . "\n";
