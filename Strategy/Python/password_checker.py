from password_validator_strategy import PasswordValidatorStrategy


class Password<PERSON>he<PERSON>:
    """Context class that uses a strategy to validate passwords."""
    
    def __init__(self, validator: PasswordValidatorStrategy):
        """
        Initialize the password checker with a validation strategy.
        
        Args:
            validator: The password validation strategy to use
        """
        self._validator = validator
    
    def check(self, password: str) -> bool:
        """
        Check if a password is valid using the configured strategy.
        
        Args:
            password: The password string to validate
            
        Returns:
            True if the password is valid, False otherwise
        """
        return self._validator.is_valid(password)
