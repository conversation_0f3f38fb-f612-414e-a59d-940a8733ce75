# Foundational Patterns

| **Pattern**                 | **Category** | **Description**                                                             | **Example Exercise**                                                                 |
|-----------------------------|--------------|-----------------------------------------------------------------------------|--------------------------------------------------------------------------------------|
| **Factory Method**          | Creational   | Lets subclasses decide which class to instantiate                           | Create a parser interface where JSON, XML, and YAML parsers are produced differently |
| **Singleton**               | Creational   | Ensures a class has only one instance                                       | Create a `Logger` class with a single shared instance used app-wide                  |
| **Adapter**                 | Structural   | Converts one interface to another                                           | Integrate a legacy payment API into a new e-commerce system                          |
| **Composite**               | Structural   | Treats individual and composite objects uniformly                           | Build a file system where `File` and `Folder` implement a common `Component`         |
| **Decorator**               | Structural   | Adds behavior to objects dynamically                                        | Add logging, caching, or authentication to a `Service` interface                     |
| **Command**                 | Behavioral   | Encapsulates a request as an object                                         | Build an undo/redo system for a text editor using `Command` objects                  |
| **Observer**                | Behavioral   | Defines a dependency between objects so they are notified of changes        | Create a `StockTicker` with observers updating different UI widgets                  |
| **Strategy**                | Behavioral   | Defines a family of interchangeable algorithms                              | Implement a `Sorter` interface that can switch between quicksort, mergesort, etc.    |


# Intermediate Patterns

| **Pattern**                 | **Category** | **Description**                                                             | **Example Exercise**                                                                 |
|-----------------------------|--------------|-----------------------------------------------------------------------------|--------------------------------------------------------------------------------------|
| **Builder**                 | Creational   | Separates object construction from its representation                       | Build a configurable `HTTPClient` with optional headers, auth, timeouts              |
| **Facade**                  | Structural   | Provides a simplified interface to a complex subsystem                      | Wrap a multimedia library with `VideoPlayerFacade` that hides complex APIs           |
| **Proxy**                   | Structural   | Provides a placeholder or control access to another object                  | Implement a `VirtualProxy` that lazily loads large images in a gallery viewer        |
| **State**                   | Behavioral   | Allows object behavior to change based on internal state                    | Model a `TCPConnection` that changes behavior based on current state                 |
| **Template Method**         | Behavioral   | Defines the skeleton of an algorithm, deferring steps to subclasses         | Build a `ReportGenerator` with steps overridden for PDF or HTML output               |

&nbsp;

&nbsp;

&nbsp;

# All Patterns

| **Pattern**                 | **Category** | **Description**                                                             | **Example Exercise**                                                                 |
|-----------------------------|--------------|-----------------------------------------------------------------------------|--------------------------------------------------------------------------------------|
| **Abstract Factory**        | Creational   | Creates families of related objects without specifying concrete classes     | Implement a GUI toolkit that supports multiple themes (e.g., DarkThemeFactory)       |
| **Builder**                 | Creational   | Separates object construction from its representation                       | Build a configurable `HTTPClient` with optional headers, auth, timeouts              |
| **Factory Method**          | Creational   | Lets subclasses decide which class to instantiate                           | Create a parser interface where JSON, XML, and YAML parsers are produced differently |
| **Prototype**               | Creational   | Clones existing objects instead of creating new ones                        | Implement a cloneable `Document` editor with formatting, metadata, etc.              |
| **Singleton**               | Creational   | Ensures a class has only one instance                                       | Create a `Logger` class with a single shared instance used app-wide                  |
| **Adapter**                 | Structural   | Converts one interface to another                                           | Integrate a legacy payment API into a new e-commerce system                          |
| **Bridge**                  | Structural   | Separates abstraction from implementation                                   | Build a `RemoteControl` interface that can work with multiple `Device` types         |
| **Composite**               | Structural   | Treats individual and composite objects uniformly                           | Build a file system where `File` and `Folder` implement a common `Component`         |
| **Decorator**               | Structural   | Adds behavior to objects dynamically                                        | Add logging, caching, or authentication to a `Service` interface                     |
| **Facade**                  | Structural   | Provides a simplified interface to a complex subsystem                      | Wrap a multimedia library with `VideoPlayerFacade` that hides complex APIs           |
| **Flyweight**               | Structural   | Shares state across many small objects to reduce memory                     | Create a `Character` object for text editor that shares font info across instances   |
| **Proxy**                   | Structural   | Provides a placeholder or control access to another object                  | Implement a `VirtualProxy` that lazily loads large images in a gallery viewer        |
| **Chain of Responsibility** | Behavioral   | Passes request along a chain of handlers                                    | Create a support system where `Handler`s can escalate requests to higher levels      |
| **Command**                 | Behavioral   | Encapsulates a request as an object                                         | Build an undo/redo system for a text editor using `Command` objects                  |
| **Interpreter**             | Behavioral   | Defines grammar and interpreter for a language                              | Implement a basic calculator that parses and evaluates math expressions              |
| **Iterator**                | Behavioral   | Provides a way to access elements without exposing the underlying structure | Write a custom iterator for traversing a tree of DOM-like nodes                      |
| **Mediator**                | Behavioral   | Centralizes complex communications between objects                          | Build a `ChatRoom` where users send messages through a `Mediator`                    |
| **Memento**                 | Behavioral   | Captures and restores an object’s internal state                            | Implement state save/restore for a form editor with undo functionality               |
| **Observer**                | Behavioral   | Defines a dependency between objects so they are notified of changes        | Create a `StockTicker` with observers updating different UI widgets                  |
| **State**                   | Behavioral   | Allows object behavior to change based on internal state                    | Model a `TCPConnection` that changes behavior based on current state                 |
| **Strategy**                | Behavioral   | Defines a family of interchangeable algorithms                              | Implement a `Sorter` interface that can switch between quicksort, mergesort, etc.    |
| **Template Method**         | Behavioral   | Defines the skeleton of an algorithm, deferring steps to subclasses         | Build a `ReportGenerator` with steps overridden for PDF or HTML output               |
| **Visitor**                 | Behavioral   | Adds operations to object structures without modifying them                 | Implement a `Visitor` that traverses an AST to generate code or analyze nodes        |
